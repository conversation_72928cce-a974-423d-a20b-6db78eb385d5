<template>
  <ContentContainer
    title="图片管理"
    description="管理和查看患者上传的医疗影像资料"
    :show-header="true"
    :breadcrumbs="breadcrumbs"
    :show-breadcrumb="true"
  >
    <!-- 搜索表单 -->
    <SearchForm
      :search-fields="searchFields"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 图片管理容器 -->
    <div class="standard-list-container">
      <div class="standard-toolbar">
        <h3 class="standard-toolbar-title">图片管理</h3>
        <div class="standard-toolbar-actions">
          <el-radio-group v-model="viewMode" size="small">
            <el-radio-button value="grid">网格视图</el-radio-button>
            <el-radio-button value="list">列表视图</el-radio-button>
          </el-radio-group>
          <el-button
            type="primary"
            @click="handleUpload"
            icon="Upload"
            size="small"
          >
            上传图片
          </el-button>
          <el-button
            type="info"
            @click="testImageService"
            icon="Connection"
            size="small"
          >
            测试图片服务
          </el-button>
          <el-button
            type="warning"
            @click="adjustGridScrolling"
            icon="Refresh"
            size="small"
          >
            检查滚动
          </el-button>
        </div>
      </div>

      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="standard-content-area" v-loading="loading">
        <div class="standard-scroll-container">
          <div class="standard-grid-layout image-grid">
        <!-- 调试信息 -->
        <div style="grid-column: 1 / -1; background: #f0f0f0; padding: 10px; margin-bottom: 10px; border-radius: 5px;">
          <strong>调试信息：</strong>
          总共 {{ images.length }} 张图片，
          当前视图模式：{{ viewMode }}
          <br>
          <strong>图片ID列表：</strong> {{ images.map(img => img.image_id).join(', ') }}
          <br>
          <strong>滚动提示：</strong> 如果最后一张图片显示不完整，请检查容器高度和底部内边距
        </div>

        <!-- 简单的数据验证 -->
        <div style="grid-column: 1 / -1; background: #e6f7ff; padding: 10px; margin-bottom: 10px; border-radius: 5px;">
          <strong>数据验证：</strong>
          <div v-for="(image, index) in images" :key="`debug-${index}`" style="margin: 5px 0;">
            {{ index + 1 }}. ID: {{ image.image_id }}, 名称: {{ image.original_name }}, URL: {{ image.image_url ? '有' : '无' }}
          </div>
        </div>

        <div
          v-for="(image, index) in images"
          :key="`grid-${image.image_id}-${index}`"
          class="image-card"
          :style="{ border: index >= 2 ? '2px solid orange' : '1px solid #ddd' }"
          :data-index="index"
          :data-id="image.image_id"
        >
          <div class="image-wrapper">
            <img
              :src="getImageUrl(image.image_url)"
              :alt="image.original_name"
              class="image-preview"
              @click="previewImage(getImageUrl(image.image_url), image)"
              @error="handleImageError"
              @load="(e) => console.log('图片加载成功:', image.image_id, e.target.src)"
              :style="{ border: '1px solid #ddd' }"
            />
            <!-- 如果图片加载失败，显示占位符 -->
            <div v-if="!getImageUrl(image.image_url)" class="image-placeholder">
              <span>图片URL缺失</span>
            </div>
            <div class="image-overlay">
              <el-button
                type="primary"
                size="small"
                @click="previewImage(getImageUrl(image.image_url), image)"
                icon="View"
                circle
              />
              <el-button
                type="danger"
                size="small"
                @click="deleteImage(image)"
                icon="Delete"
                circle
              />
            </div>
          </div>
          <div class="image-info">
            <div class="image-title">{{ image.original_name }}</div>
            <div class="image-meta">
              <span class="meta-item">ID: {{ image.image_id }}</span>
              <span class="meta-item">索引: {{ index }}</span>
              <span class="meta-item">患者ID: {{ image.patient_id || '未关联' }}</span>
              <span class="meta-item">大小: {{ formatFileSize(image.file_size) }}</span>
              <span class="meta-item">上传时间: {{ formatDate(image.created_at) }}</span>
            </div>
          </div>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <DataTable
        v-else
        :data="images"
        :columns="tableColumns"
        :loading="loading"
        :pagination="pagination"
        title="图片列表"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        @refresh="fetchImages"
      >
        <!-- 自定义图片预览列 -->
        <template #image_preview="{ row }">
          <img
            :src="getImageUrl(row.image_url)"
            :alt="row.original_name"
            class="table-image-preview"
            @click="previewImage(getImageUrl(row.image_url), row)"
            @error="handleImageError"
          />
        </template>

        <!-- 自定义操作列 -->
        <template #actions="{ row }">
          <el-button
            type="text"
            size="small"
            @click="previewImage(getImageUrl(row.image_url), row)"
            icon="View"
          >
            预览
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="downloadImage(row)"
            icon="Download"
          >
            下载
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="deleteImage(row)"
            class="text-error"
            icon="Delete"
          >
            删除
          </el-button>
        </template>
      </DataTable>

      <!-- 空状态 -->
      <el-empty
        v-if="!loading && images.length === 0"
        description="暂无图片资料"
        class="empty-state"
      >
        <el-button type="primary" @click="handleUpload">上传第一张图片</el-button>
      </el-empty>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="currentImage?.original_name || '图片预览'"
      width="80%"
      center
    >
      <div class="preview-container">
        <img
          :src="dialogImageUrl"
          :alt="currentImage?.original_name"
          class="preview-image"
        />
        <div v-if="currentImage" class="preview-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="文件名">{{ currentImage.original_name }}</el-descriptions-item>
            <el-descriptions-item label="文件大小">{{ formatFileSize(currentImage.file_size) }}</el-descriptions-item>
            <el-descriptions-item label="患者ID">{{ currentImage.patient_id || '未关联' }}</el-descriptions-item>
            <el-descriptions-item label="上传时间">{{ formatDate(currentImage.created_at) }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <template #footer>
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button
          v-if="currentImage"
          type="primary"
          @click="downloadImage(currentImage)"
        >
          下载
        </el-button>
      </template>
    </el-dialog>
  </ContentContainer>
</template>

<script>
import { defineComponent, ref, onMounted, computed, watch, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import api from '../api';
import ContentContainer from '../components/ContentContainer.vue';
import SearchForm from '../components/SearchForm.vue';
import DataTable from '../components/DataTable.vue';

export default defineComponent({
  components: {
    ContentContainer,
    SearchForm,
    DataTable
  },
  setup() {
    const images = ref([]);
    const loading = ref(false);
    const searchParams = ref({});
    const viewMode = ref('grid'); // 'grid' | 'list'
    const dialogVisible = ref(false);
    const dialogImageUrl = ref('');
    const currentImage = ref(null);

    // 分页配置
    const pagination = ref({
      page: 1,
      pageSize: 12, // 网格视图适合更多数量
      total: 0
    });

    // 面包屑导航
    const breadcrumbs = computed(() => [
      { title: '首页', path: '/' },
      { title: '图片管理', path: '/images' }
    ]);

    // 搜索字段配置
    const searchFields = computed(() => [
      {
        key: 'patient_id',
        label: '患者ID',
        type: 'input',
        placeholder: '请输入患者ID',
        clearable: true
      },
      {
        key: 'filename',
        label: '文件名',
        type: 'input',
        placeholder: '请输入文件名',
        clearable: true
      },
      {
        key: 'date_range',
        label: '上传时间',
        type: 'daterange',
        placeholder: '选择时间范围',
        clearable: true
      }
    ]);

    // 表格列配置（列表视图）
    const tableColumns = computed(() => [
      {
        prop: 'image_preview',
        label: '预览',
        width: 80,
        align: 'center',
        slot: 'image_preview'
      },
      {
        prop: 'original_name',
        label: '文件名',
        minWidth: 150,
        showOverflowTooltip: true
      },
      {
        prop: 'patient_id',
        label: '患者ID',
        width: 100,
        align: 'center',
        formatter: (row) => row.patient_id || '未关联'
      },
      {
        prop: 'file_size',
        label: '文件大小',
        width: 100,
        align: 'center',
        formatter: (row) => formatFileSize(row.file_size)
      },
      {
        prop: 'created_at',
        label: '上传时间',
        width: 180,
        align: 'center',
        formatter: (row) => formatDate(row.created_at)
      }
    ]);

    // 获取图片列表
    const fetchImages = async () => {
      loading.value = true;
      try {
        const params = {
          page: pagination.value.page,
          limit: pagination.value.pageSize,
          ...searchParams.value
        };

        const response = await api.get('/image/list', { params });

        console.log('图片列表响应:', response);

        if (response.images) {
          images.value = response.images;
          pagination.value.total = response.total;
          console.log('成功加载图片:', images.value.length, '张');
          console.log('图片数据详情:', images.value);

          // 检查每张图片的关键字段
          images.value.forEach((img, index) => {
            console.log(`图片 ${index + 1}:`, {
              id: img.image_id,
              name: img.original_name,
              url: img.image_url,
              hasRequiredFields: !!(img.image_id && img.original_name && img.image_url)
            });
          });
        } else {
          images.value = [];
          pagination.value.total = 0;
          console.log('响应中没有图片数据:', response);
          ElMessage.error(response.message || '获取图片列表失败');
        }
      } catch (error) {
        console.error('获取图片列表失败:', error);

        // 更详细的错误处理
        let errorMessage = '获取图片列表失败';
        if (error.message.includes('认证失败')) {
          errorMessage = '认证失败，请重新登录';
        } else if (error.message.includes('网络错误')) {
          errorMessage = '网络连接失败，请检查网络连接';
        } else if (error.message.includes('服务器错误')) {
          errorMessage = '服务器错误，请稍后重试';
        }

        ElMessage.error(errorMessage);
        images.value = [];
        pagination.value.total = 0;
      } finally {
        loading.value = false;
      }
    };

    // 搜索处理
    const handleSearch = (searchData) => {
      searchParams.value = searchData;
      pagination.value.page = 1;
      fetchImages();
    };

    // 重置搜索
    const handleReset = () => {
      searchParams.value = {};
      pagination.value.page = 1;
      fetchImages();
    };

    // 分页处理
    const handleSizeChange = (newSize) => {
      pagination.value.pageSize = newSize;
      pagination.value.page = 1;
      fetchImages();
    };

    const handleCurrentChange = (newPage) => {
      pagination.value.page = newPage;
      fetchImages();
    };

    // 获取图片URL
    const getImageUrl = (url) => {
      if (!url) {
        console.warn('图片URL为空');
        return '';
      }

      // 如果是完整URL，直接返回
      if (url.startsWith('http')) {
        console.log('使用完整URL:', url);
        return url;
      }

      // 否则拼接基础URL
      const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';
      const fullUrl = `${baseUrl}${url}`;
      console.log('拼接图片URL:', { baseUrl, url, fullUrl });
      return fullUrl;
    };

    // 图片预览
    const previewImage = (url, image = null) => {
      dialogImageUrl.value = url;
      currentImage.value = image;
      dialogVisible.value = true;
    };

    // 删除图片
    const deleteImage = async (image) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除图片 "${image.original_name}" 吗？此操作不可恢复。`,
          '删除确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );

        const response = await api.delete(`/image/${image.image_id}`);
        if (response.success) {
          ElMessage.success('图片删除成功');
          fetchImages(); // 刷新列表
        } else {
          ElMessage.error(response.message || '删除图片失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除图片失败:', error);
          ElMessage.error('删除图片失败，请稍后重试');
        }
      }
    };

    // 下载图片
    const downloadImage = (image) => {
      const url = getImageUrl(image.image_url);
      const link = document.createElement('a');
      link.href = url;
      link.download = image.original_name;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    };

    // 上传图片
    const handleUpload = () => {
      // TODO: 实现上传功能
      ElMessage.info('上传功能开发中...');
    };

    // 测试图片服务
    const testImageService = async () => {
      try {
        const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';

        // 测试调试接口
        const debugResponse = await fetch(`${baseUrl}/api/debug/images`);
        const debugData = await debugResponse.json();

        console.log('图片服务调试信息:', debugData);

        ElMessage.success(`图片服务正常！找到 ${debugData.count} 个图片文件`);

        // 如果有图片，测试第一个图片的访问
        if (debugData.files && debugData.files.length > 0) {
          const testImageUrl = `${baseUrl}/upload/images/${debugData.files[0]}`;
          console.log('测试图片URL:', testImageUrl);

          // 尝试加载第一个图片
          const img = new Image();
          img.onload = () => {
            console.log('图片加载成功:', testImageUrl);
            ElMessage.success('图片访问测试成功！');
          };
          img.onerror = () => {
            console.error('图片加载失败:', testImageUrl);
            ElMessage.error('图片访问测试失败！');
          };
          img.src = testImageUrl;
        }

      } catch (error) {
        console.error('图片服务测试失败:', error);
        ElMessage.error(`图片服务测试失败: ${error.message}`);
      }
    };

    // 图片加载错误处理
    const handleImageError = (event) => {
      console.error('图片加载失败:', event.target.src);
      // 创建一个简单的占位图
      const canvas = document.createElement('canvas');
      canvas.width = 200;
      canvas.height = 200;
      const ctx = canvas.getContext('2d');

      // 绘制灰色背景
      ctx.fillStyle = '#f5f5f5';
      ctx.fillRect(0, 0, 200, 200);

      // 绘制边框
      ctx.strokeStyle = '#ddd';
      ctx.lineWidth = 2;
      ctx.strokeRect(1, 1, 198, 198);

      // 绘制文字
      ctx.fillStyle = '#999';
      ctx.font = '14px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('图片加载失败', 100, 90);
      ctx.fillText('Image Load Error', 100, 110);

      // 设置为占位图
      event.target.src = canvas.toDataURL();
      event.target.style.objectFit = 'contain';
    };

    // 格式化文件大小
    const formatFileSize = (bytes) => {
      if (!bytes) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '-';
      return new Date(dateString).toLocaleString('zh-CN');
    };

    // 监控images数组变化
    watch(images, (newImages, oldImages) => {
      console.log('Images数组发生变化:', {
        新数量: newImages.length,
        旧数量: oldImages ? oldImages.length : 0,
        新数据: newImages,
        旧数据: oldImages
      });
    }, { deep: true });

    // 监控viewMode变化
    watch(viewMode, (newMode, oldMode) => {
      console.log('视图模式变化:', { 新模式: newMode, 旧模式: oldMode });
      // 强制重新渲染
      nextTick(() => {
        console.log('视图切换后的images数量:', images.value.length);
        if (newMode === 'grid') {
          // 网格视图切换后，确保滚动容器正确设置
          adjustGridScrolling();
        }
      });
    });

    // 调整网格滚动的方法
    const adjustGridScrolling = () => {
      nextTick(() => {
        const gridElement = document.querySelector('.image-grid');
        if (gridElement) {
          // 记录滚动容器信息
          console.log('网格容器信息:', {
            scrollHeight: gridElement.scrollHeight,
            clientHeight: gridElement.clientHeight,
            scrollTop: gridElement.scrollTop,
            可滚动距离: gridElement.scrollHeight - gridElement.clientHeight
          });

          // 确保有足够的滚动空间
          const lastCard = gridElement.querySelector('.image-card:last-child');
          if (lastCard) {
            const cardRect = lastCard.getBoundingClientRect();
            const gridRect = gridElement.getBoundingClientRect();
            console.log('最后一张图片位置:', {
              卡片底部: cardRect.bottom,
              容器底部: gridRect.bottom,
              是否被遮挡: cardRect.bottom > gridRect.bottom
            });
          }
        }
      });
    };

    onMounted(() => {
      fetchImages();
    });

    return {
      images,
      loading,
      viewMode,
      pagination,
      breadcrumbs,
      searchFields,
      tableColumns,
      dialogVisible,
      dialogImageUrl,
      currentImage,
      fetchImages,
      handleSearch,
      handleReset,
      handleSizeChange,
      handleCurrentChange,
      getImageUrl,
      previewImage,
      deleteImage,
      downloadImage,
      handleUpload,
      testImageService,
      adjustGridScrolling,
      handleImageError,
      formatFileSize,
      formatDate
    };
  }
});
</script>

<style scoped>
/* 使用标准化样式，移除重复的样式定义 */

/* 图片网格特定样式 */
.image-grid {
  /* 继承标准网格布局，添加特定的行高设置 */
  grid-auto-rows: minmax(300px, auto);
  position: relative;
  z-index: var(--z-grid-container);
}

/* 确保网格容器有足够的滚动空间 */
.image-grid::after {
  content: '';
  grid-column: 1 / -1;
  height: var(--spacing-lg); /* 使用标准间距 */
  display: block;
}

.image-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: var(--transition-normal);
  box-shadow: var(--shadow-sm);

  /* 确保卡片可见性 */
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  min-height: 300px;
}

.image-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.image-wrapper {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  background: var(--bg-tertiary);
}

.image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: var(--transition-normal);
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  color: #999;
  font-size: 14px;
}

.image-preview:hover {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  opacity: 0;
  transition: var(--transition-normal);
}

.image-wrapper:hover .image-overlay {
  opacity: 1;
}

.image-info {
  padding: var(--spacing-md);
}

.image-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.image-meta {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.meta-item {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

/* 列表视图中的图片预览 */
.table-image-preview {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: var(--transition-normal);
}

.table-image-preview:hover {
  transform: scale(1.1);
}

/* 预览对话框样式 */
.preview-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.preview-image {
  max-width: 100%;
  max-height: 60vh;
  object-fit: contain;
  margin: 0 auto;
  display: block;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
}

.preview-info {
  background: var(--bg-tertiary);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
}

/* 空状态样式 */
.empty-state {
  padding: var(--spacing-xxl);
}

/* 响应式设计已在标准化样式中定义 */

/* Element Plus 样式覆盖 */
:deep(.el-dialog__body) {
  padding: var(--spacing-lg);
}

:deep(.el-descriptions) {
  margin-top: var(--spacing-md);
}

.text-error {
  color: var(--error-color) !important;
}
</style>